"""Dialog for creating new Minecraft servers."""

import os
from PySide6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QFormLayout,
    QLineEdit, QSpinBox, QComboBox, QFileDialog,
    QPushButton, QLabel, QTextEdit, QGroupBox,
    QSlider, QCheckBox, QMessageBox, QRadioButton,
    QButtonGroup, QFrame
)
from PySide6.QtCore import Qt, QTimer
from minecraft_server_manager.core.downloader import ServerDownloader
from .download_dialog import DownloadDialog


class ServerCreationDialog(QDialog):
    """Dialog for creating a new Minecraft server."""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("創建新伺服器")
        self.setModal(True)
        self.resize(550, 700)

        # Initialize downloader
        self.downloader = ServerDownloader()

        # Get current working directory for default server location
        self.default_servers_dir = os.path.join(os.getcwd(), "servers")

        self.setup_ui()
    
    def setup_ui(self):
        """Set up the dialog UI."""
        layout = QVBoxLayout(self)

        # Basic settings group
        basic_group = QGroupBox("基本設定")
        basic_layout = QFormLayout(basic_group)

        # Server name
        self.name_edit = QLineEdit()
        self.name_edit.setPlaceholderText("輸入伺服器名稱")
        self.name_edit.textChanged.connect(self.update_folder_suggestion)
        basic_layout.addRow("伺服器名稱:", self.name_edit)

        # Server folder (auto-generated based on name)
        folder_layout = QVBoxLayout()

        # Folder path display
        folder_path_layout = QHBoxLayout()
        self.folder_edit = QLineEdit()
        self.folder_edit.setPlaceholderText("伺服器資料夾路徑")
        self.folder_btn = QPushButton("瀏覽...")
        self.folder_btn.clicked.connect(self.browse_folder)
        folder_path_layout.addWidget(self.folder_edit)
        folder_path_layout.addWidget(self.folder_btn)
        folder_layout.addLayout(folder_path_layout)

        # Auto-generate folder checkbox
        self.auto_folder_check = QCheckBox("自動在 servers 資料夾下創建")
        self.auto_folder_check.setChecked(True)
        self.auto_folder_check.toggled.connect(self.toggle_auto_folder)
        folder_layout.addWidget(self.auto_folder_check)

        basic_layout.addRow("資料夾:", folder_layout)

        layout.addWidget(basic_group)
        
        # Java settings group
        java_group = QGroupBox("Java 設定")
        java_layout = QFormLayout(java_group)
        
        # Java path
        java_path_layout = QHBoxLayout()
        self.java_edit = QLineEdit()
        self.java_edit.setPlaceholderText("Java 執行檔路徑 (留空使用系統預設)")
        self.java_btn = QPushButton("瀏覽...")
        self.java_btn.clicked.connect(self.browse_java)
        java_path_layout.addWidget(self.java_edit)
        java_path_layout.addWidget(self.java_btn)
        java_layout.addRow("Java 路徑:", java_path_layout)
        
        # RAM settings
        ram_layout = QVBoxLayout()
        self.ram_slider = QSlider(Qt.Horizontal)
        self.ram_slider.setMinimum(512)
        self.ram_slider.setMaximum(16384)
        self.ram_slider.setValue(2048)
        self.ram_slider.valueChanged.connect(self.update_ram_label)
        
        self.ram_label = QLabel("2048 MB")
        ram_layout.addWidget(self.ram_slider)
        ram_layout.addWidget(self.ram_label)
        java_layout.addRow("記憶體 (RAM):", ram_layout)
        
        # JVM arguments
        self.jvm_args_edit = QTextEdit()
        self.jvm_args_edit.setMaximumHeight(80)
        self.jvm_args_edit.setPlaceholderText("額外的 JVM 參數 (可選)")
        java_layout.addRow("JVM 參數:", self.jvm_args_edit)
        
        layout.addWidget(java_group)
        
        # Server settings group
        server_group = QGroupBox("伺服器設定")
        server_layout = QFormLayout(server_group)

        # Server type (moved up to affect version loading)
        self.server_type_combo = QComboBox()
        self.server_type_combo.addItems([
            "Vanilla",
            "Paper",
            "Fabric",
            "Forge",
            "Modpack"
        ])
        self.server_type_combo.currentTextChanged.connect(self.update_versions)
        server_layout.addRow("伺服器類型:", self.server_type_combo)

        # Game version (dynamic based on server type)
        version_layout = QHBoxLayout()
        self.version_combo = QComboBox()
        self.refresh_versions_btn = QPushButton("🔄")
        self.refresh_versions_btn.setMaximumWidth(30)
        self.refresh_versions_btn.setToolTip("重新整理版本列表")
        self.refresh_versions_btn.clicked.connect(self.refresh_versions)
        version_layout.addWidget(self.version_combo)
        version_layout.addWidget(self.refresh_versions_btn)
        server_layout.addRow("遊戲版本:", version_layout)

        # Download options
        download_group = QGroupBox("下載選項")
        download_layout = QFormLayout(download_group)

        self.auto_download_check = QCheckBox("自動下載伺服器檔案")
        self.auto_download_check.setChecked(True)
        self.auto_download_check.toggled.connect(self.toggle_download_options)
        download_layout.addRow("", self.auto_download_check)

        # Modpack URL (only for modpack type)
        self.modpack_url_edit = QLineEdit()
        self.modpack_url_edit.setPlaceholderText("輸入 Modpack 下載連結 (CurseForge, Modrinth 等)")
        self.modpack_url_edit.setVisible(False)
        download_layout.addRow("Modpack 連結:", self.modpack_url_edit)

        server_layout.addRow("", download_group)
        
        # Server port
        self.port_spin = QSpinBox()
        self.port_spin.setMinimum(1)
        self.port_spin.setMaximum(65535)
        self.port_spin.setValue(25565)
        server_layout.addRow("連接埠:", self.port_spin)
        
        # Auto start
        self.auto_start_check = QCheckBox("自動接受 EULA")
        server_layout.addRow("", self.auto_start_check)
        
        layout.addWidget(server_group)

        # Buttons
        button_layout = QHBoxLayout()

        self.create_btn = QPushButton("創建")
        self.create_btn.clicked.connect(self.accept)
        self.create_btn.setDefault(True)

        self.cancel_btn = QPushButton("取消")
        self.cancel_btn.clicked.connect(self.reject)

        button_layout.addStretch()
        button_layout.addWidget(self.create_btn)
        button_layout.addWidget(self.cancel_btn)

        layout.addLayout(button_layout)

        # Initialize versions for default server type
        self.update_versions()
        self.update_folder_suggestion()

    def update_folder_suggestion(self):
        """Update folder suggestion based on server name."""
        if self.auto_folder_check.isChecked():
            server_name = self.name_edit.text().strip()
            if server_name:
                # Create safe folder name
                safe_name = "".join(c for c in server_name if c.isalnum() or c in (' ', '-', '_')).strip()
                safe_name = safe_name.replace(' ', '_')
                folder_path = os.path.join(self.default_servers_dir, safe_name)
                self.folder_edit.setText(folder_path)

    def toggle_auto_folder(self, checked):
        """Toggle auto folder generation."""
        if checked:
            self.update_folder_suggestion()
            self.folder_edit.setEnabled(False)
            self.folder_btn.setEnabled(False)
        else:
            self.folder_edit.setEnabled(True)
            self.folder_btn.setEnabled(True)

    def update_versions(self):
        """Update version list based on server type."""
        server_type = self.server_type_combo.currentText()

        # Show/hide modpack URL
        is_modpack = server_type == "Modpack"
        self.modpack_url_edit.setVisible(is_modpack)

        # Clear and populate versions
        self.version_combo.clear()

        if is_modpack:
            self.version_combo.addItems(["自訂 Modpack"])
            self.version_combo.setEnabled(False)
        else:
            self.version_combo.setEnabled(True)
            try:
                versions = self.downloader.get_available_versions(server_type)
                self.version_combo.addItems(versions)
            except Exception:
                # Fallback versions
                self.version_combo.addItems([
                    "1.20.4", "1.20.3", "1.20.2", "1.20.1", "1.20"
                ])

    def refresh_versions(self):
        """Refresh version list."""
        self.refresh_versions_btn.setEnabled(False)
        self.refresh_versions_btn.setText("⏳")

        # Use QTimer to update UI after a short delay
        QTimer.singleShot(100, self._do_refresh_versions)

    def _do_refresh_versions(self):
        """Actually refresh the versions."""
        try:
            self.update_versions()
        finally:
            self.refresh_versions_btn.setEnabled(True)
            self.refresh_versions_btn.setText("🔄")

    def toggle_download_options(self, checked):
        """Toggle download options visibility."""
        # Could add more download-specific options here
        pass

    def browse_folder(self):
        """Browse for server folder."""
        folder = QFileDialog.getExistingDirectory(
            self, "選擇伺服器資料夾",
            self.default_servers_dir
        )
        if folder:
            self.folder_edit.setText(folder)
            self.auto_folder_check.setChecked(False)
    
    def browse_java(self):
        """Browse for Java executable."""
        java_file, _ = QFileDialog.getOpenFileName(
            self, "選擇 Java 執行檔", 
            os.path.expanduser("~"),
            "執行檔 (*.exe);;所有檔案 (*.*)"
        )
        if java_file:
            self.java_edit.setText(java_file)
    
    def update_ram_label(self, value):
        """Update RAM label when slider changes."""
        self.ram_label.setText(f"{value} MB")
    
    def get_server_config(self):
        """Get the server configuration from the dialog."""
        return {
            'name': self.name_edit.text(),
            'folder': self.folder_edit.text(),
            'java_path': self.java_edit.text() or 'java',
            'ram_mb': self.ram_slider.value(),
            'jvm_args': self.jvm_args_edit.toPlainText(),
            'version': self.version_combo.currentText(),
            'server_type': self.server_type_combo.currentText(),
            'port': self.port_spin.value(),
            'auto_eula': self.auto_start_check.isChecked(),
            'auto_download': self.auto_download_check.isChecked(),
            'modpack_url': self.modpack_url_edit.text() if self.server_type_combo.currentText() == "Modpack" else ""
        }
    
    def accept(self):
        """Validate and accept the dialog."""
        if not self.name_edit.text().strip():
            QMessageBox.warning(self, "錯誤", "請輸入伺服器名稱")
            return

        if not self.folder_edit.text().strip():
            QMessageBox.warning(self, "錯誤", "請選擇伺服器資料夾")
            return

        # Validate modpack URL if needed
        if self.server_type_combo.currentText() == "Modpack":
            if not self.modpack_url_edit.text().strip():
                QMessageBox.warning(self, "錯誤", "請輸入 Modpack 下載連結")
                return

        # Create server folder if it doesn't exist
        folder_path = self.folder_edit.text()
        try:
            os.makedirs(folder_path, exist_ok=True)
        except Exception as e:
            QMessageBox.critical(self, "錯誤", f"無法創建資料夾: {str(e)}")
            return

        # Start download if auto download is enabled
        if self.auto_download_check.isChecked() and self.server_type_combo.currentText() != "Modpack":
            self.start_server_download()
        else:
            super().accept()

    def start_server_download(self):
        """Start downloading the server JAR file."""
        server_type = self.server_type_combo.currentText()
        version = self.version_combo.currentText()
        folder_path = self.folder_edit.text()

        # Get download worker
        download_worker = self.downloader.download_server(server_type, version, folder_path)

        if not download_worker:
            # Manual download needed or error
            if server_type == "Forge":
                QMessageBox.information(
                    self, "手動下載",
                    f"Forge 伺服器需要手動下載。\n\n"
                    f"請前往 https://files.minecraftforge.net/ \n"
                    f"下載 {version} 版本的 Forge 伺服器檔案，\n"
                    f"並將其放置在: {folder_path}"
                )
            else:
                QMessageBox.warning(self, "下載錯誤", "無法獲取下載資訊，請手動下載伺服器檔案")
            super().accept()
            return

        # Show download dialog
        download_dialog = DownloadDialog(self)
        download_dialog.start_download(download_worker)

        if download_dialog.exec():
            # Download completed successfully
            super().accept()
        # If dialog was cancelled, stay in creation dialog
