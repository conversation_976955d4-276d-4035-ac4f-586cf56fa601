#!/usr/bin/env python3
"""Demo script showcasing new features."""

import sys
import os
from PySide6.QtWidgets import QApplication, QMessageBox
from minecraft_server_manager.gui.main_window import MainWindow


def demo_new_features():
    """Demo the new features."""
    app = QApplication(sys.argv)
    
    # Create main window
    window = MainWindow()
    
    # Show welcome message with new features
    welcome_msg = """
🎉 Minecraft Server Manager - 新功能展示

✨ 新增功能：

🗂️ 智慧資料夾管理
• 自動在 servers/ 目錄下創建伺服器資料夾
• 根據伺服器名稱自動命名
• 同層級管理，便於備份和維護

📥 自動下載系統
• 支援 Vanilla、Paper、Fabric 自動下載
• 即時獲取最新版本列表
• 下載進度顯示和錯誤處理
• Forge 提供手動下載指引

📦 Modpack 支援
• 支援 CurseForge、Modrinth 等平台
• 直接輸入下載連結創建伺服器
• 未來將支援自動解壓和配置

🔧 改進的使用者介面
• 更直觀的伺服器類型選擇
• 版本列表即時更新
• 智慧化的設定選項

現在就來試試創建一個新伺服器吧！
點擊「創建新伺服器」按鈕開始體驗。
    """
    
    QMessageBox.information(window, "🎉 新功能展示", welcome_msg)
    
    # Show the main window
    window.show()
    
    # Create servers directory if it doesn't exist
    servers_dir = os.path.join(os.getcwd(), "servers")
    os.makedirs(servers_dir, exist_ok=True)
    
    # Start event loop
    sys.exit(app.exec())


if __name__ == "__main__":
    demo_new_features()
