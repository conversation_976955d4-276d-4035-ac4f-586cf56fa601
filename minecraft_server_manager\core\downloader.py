"""Server JAR downloader for various Minecraft server types."""

import os
import json
import requests
from typing import Dict, List, Optional, Tuple
from PySide6.QtCore import QObject, QThread, Signal


class DownloadWorker(QThread):
    """Worker thread for downloading server files."""

    progress = Signal(int)  # Progress percentage
    status = Signal(str)    # Status message
    finished = Signal(bool, str)  # Success, message
    
    def __init__(self, url: str, file_path: str, file_size: int = 0):
        super().__init__()
        self.url = url
        self.file_path = file_path
        self.file_size = file_size
        self._cancelled = False
    
    def cancel(self):
        """Cancel the download."""
        self._cancelled = True
    
    def run(self):
        """Download the file."""
        try:
            self.status.emit("開始下載...")
            
            response = requests.get(self.url, stream=True)
            response.raise_for_status()
            
            total_size = int(response.headers.get('content-length', self.file_size))
            downloaded = 0
            
            os.makedirs(os.path.dirname(self.file_path), exist_ok=True)
            
            with open(self.file_path, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    if self._cancelled:
                        self.finished.emit(False, "下載已取消")
                        return
                    
                    if chunk:
                        f.write(chunk)
                        downloaded += len(chunk)
                        
                        if total_size > 0:
                            progress = int((downloaded / total_size) * 100)
                            self.progress.emit(progress)
                            self.status.emit(f"下載中... {downloaded // 1024} KB / {total_size // 1024} KB")
            
            self.finished.emit(True, "下載完成")
            
        except Exception as e:
            self.finished.emit(False, f"下載失敗: {str(e)}")


class ServerDownloader(QObject):
    """Manager for downloading Minecraft server JARs."""
    
    # API endpoints
    PAPER_API = "https://api.papermc.io/v2"
    FABRIC_API = "https://meta.fabricmc.net/v2"
    FORGE_API = "https://files.minecraftforge.net/net/minecraftforge/forge"
    VANILLA_API = "https://launchermeta.mojang.com/mc/game/version_manifest.json"
    
    def __init__(self):
        super().__init__()
        self._cache = {}
    
    def get_available_versions(self, server_type: str) -> List[str]:
        """Get available versions for a server type."""
        try:
            if server_type == "Vanilla":
                return self._get_vanilla_versions()
            elif server_type == "Paper":
                return self._get_paper_versions()
            elif server_type == "Fabric":
                return self._get_fabric_versions()
            elif server_type == "Forge":
                return self._get_forge_versions()
            else:
                return ["1.20.4", "1.20.3", "1.20.2", "1.20.1", "1.20"]
        except Exception:
            # Fallback to common versions
            return ["1.20.4", "1.20.3", "1.20.2", "1.20.1", "1.20"]
    
    def _get_vanilla_versions(self) -> List[str]:
        """Get Vanilla server versions."""
        try:
            response = requests.get(self.VANILLA_API, timeout=10)
            data = response.json()
            
            versions = []
            for version in data['versions']:
                if version['type'] == 'release':
                    versions.append(version['id'])
                    if len(versions) >= 20:  # Limit to recent versions
                        break
            
            return versions
        except Exception:
            return ["1.20.4", "1.20.3", "1.20.2", "1.20.1", "1.20"]
    
    def _get_paper_versions(self) -> List[str]:
        """Get Paper server versions."""
        try:
            response = requests.get(f"{self.PAPER_API}/projects/paper", timeout=10)
            data = response.json()
            
            # Return recent versions in reverse order
            versions = data['versions'][-20:]
            versions.reverse()
            return versions
        except Exception:
            return ["1.20.4", "1.20.3", "1.20.2", "1.20.1", "1.20"]
    
    def _get_fabric_versions(self) -> List[str]:
        """Get Fabric server versions."""
        try:
            response = requests.get(f"{self.FABRIC_API}/versions/game", timeout=10)
            data = response.json()
            
            versions = []
            for version in data:
                if version['stable']:
                    versions.append(version['version'])
                    if len(versions) >= 20:
                        break
            
            return versions
        except Exception:
            return ["1.20.4", "1.20.3", "1.20.2", "1.20.1", "1.20"]
    
    def _get_forge_versions(self) -> List[str]:
        """Get Forge server versions (simplified)."""
        # Forge API is complex, return common versions
        return ["1.20.4", "1.20.3", "1.20.2", "1.20.1", "1.20", "1.19.4", "1.19.2", "1.18.2", "1.16.5"]
    
    def get_download_info(self, server_type: str, version: str) -> Optional[Tuple[str, str, int]]:
        """Get download URL, filename, and size for a server version."""
        try:
            if server_type == "Vanilla":
                return self._get_vanilla_download_info(version)
            elif server_type == "Paper":
                return self._get_paper_download_info(version)
            elif server_type == "Fabric":
                return self._get_fabric_download_info(version)
            elif server_type == "Forge":
                return self._get_forge_download_info(version)
            else:
                return None
        except Exception:
            return None
    
    def _get_vanilla_download_info(self, version: str) -> Optional[Tuple[str, str, int]]:
        """Get Vanilla server download info."""
        try:
            # Get version manifest
            response = requests.get(self.VANILLA_API, timeout=10)
            manifest = response.json()
            
            # Find version
            version_url = None
            for v in manifest['versions']:
                if v['id'] == version:
                    version_url = v['url']
                    break
            
            if not version_url:
                return None
            
            # Get version details
            response = requests.get(version_url, timeout=10)
            version_data = response.json()
            
            server_info = version_data['downloads']['server']
            url = server_info['url']
            size = server_info['size']
            filename = f"server-{version}.jar"
            
            return url, filename, size
            
        except Exception:
            return None
    
    def _get_paper_download_info(self, version: str) -> Optional[Tuple[str, str, int]]:
        """Get Paper server download info."""
        try:
            # Get builds for version
            response = requests.get(f"{self.PAPER_API}/projects/paper/versions/{version}", timeout=10)
            data = response.json()
            
            # Get latest build
            latest_build = data['builds'][-1]
            
            # Get build details
            response = requests.get(
                f"{self.PAPER_API}/projects/paper/versions/{version}/builds/{latest_build}",
                timeout=10
            )
            build_data = response.json()
            
            # Get download info
            download = build_data['downloads']['application']
            filename = download['name']
            url = f"{self.PAPER_API}/projects/paper/versions/{version}/builds/{latest_build}/downloads/{filename}"
            
            return url, filename, 0  # Size not provided by API
            
        except Exception:
            return None
    
    def _get_fabric_download_info(self, version: str) -> Optional[Tuple[str, str, int]]:
        """Get Fabric server download info."""
        try:
            # Get loader versions
            response = requests.get(f"{self.FABRIC_API}/versions/loader", timeout=10)
            loaders = response.json()
            
            if not loaders:
                return None
            
            latest_loader = loaders[0]['version']
            
            # Get installer versions
            response = requests.get(f"{self.FABRIC_API}/versions/installer", timeout=10)
            installers = response.json()
            
            if not installers:
                return None
            
            latest_installer = installers[0]['version']
            
            # Build download URL
            filename = f"fabric-server-mc.{version}-loader.{latest_loader}-launcher.{latest_installer}.jar"
            url = f"https://meta.fabricmc.net/v2/versions/loader/{version}/{latest_loader}/{latest_installer}/server/jar"
            
            return url, filename, 0
            
        except Exception:
            return None
    
    def _get_forge_download_info(self, version: str) -> Optional[Tuple[str, str, int]]:
        """Get Forge server download info (simplified)."""
        # Forge download is complex, return a placeholder
        # In a real implementation, you'd need to parse the Forge website
        filename = f"forge-{version}-server.jar"
        return None, filename, 0  # Return None URL to indicate manual download needed
    
    def download_server(self, server_type: str, version: str, target_folder: str) -> Optional[DownloadWorker]:
        """Start downloading a server JAR."""
        download_info = self.get_download_info(server_type, version)
        
        if not download_info:
            return None
        
        url, filename, size = download_info
        
        if not url:  # Manual download needed (e.g., Forge)
            return None
        
        file_path = os.path.join(target_folder, filename)
        
        worker = DownloadWorker(url, file_path, size)
        return worker
    
    def get_modpack_sources(self) -> Dict[str, str]:
        """Get available modpack sources."""
        return {
            "CurseForge": "https://www.curseforge.com/minecraft/modpacks",
            "Modrinth": "https://modrinth.com/modpacks",
            "FTB": "https://www.feed-the-beast.com/modpacks",
            "Technic": "https://www.technicpack.net/modpacks"
        }
    
    def download_modpack(self, modpack_url: str, target_folder: str) -> Optional[DownloadWorker]:
        """Download a modpack (placeholder for future implementation)."""
        # This would need specific implementation for each modpack platform
        # For now, return None to indicate not implemented
        return None
