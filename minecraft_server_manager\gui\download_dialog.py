"""Download progress dialog for server files."""

from PySide6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QLabel, 
    QProgressBar, QPushButton, QTextEdit
)
from PySide6.QtCore import Qt
from minecraft_server_manager.core.downloader import DownloadWorker


class DownloadDialog(QDialog):
    """Dialog showing download progress."""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("下載伺服器檔案")
        self.setModal(True)
        self.setFixedSize(400, 200)
        
        self.download_worker = None
        self.setup_ui()
    
    def setup_ui(self):
        """Set up the dialog UI."""
        layout = QVBoxLayout(self)
        
        # Status label
        self.status_label = QLabel("準備下載...")
        layout.addWidget(self.status_label)
        
        # Progress bar
        self.progress_bar = QProgressBar()
        self.progress_bar.setRange(0, 100)
        self.progress_bar.setValue(0)
        layout.addWidget(self.progress_bar)
        
        # Log area (hidden by default)
        self.log_text = QTextEdit()
        self.log_text.setMaximumHeight(80)
        self.log_text.setVisible(False)
        layout.addWidget(self.log_text)
        
        # Buttons
        button_layout = QHBoxLayout()
        
        self.show_log_btn = QPushButton("顯示詳細資訊")
        self.show_log_btn.clicked.connect(self.toggle_log)
        button_layout.addWidget(self.show_log_btn)
        
        button_layout.addStretch()
        
        self.cancel_btn = QPushButton("取消")
        self.cancel_btn.clicked.connect(self.cancel_download)
        button_layout.addWidget(self.cancel_btn)
        
        self.close_btn = QPushButton("關閉")
        self.close_btn.clicked.connect(self.accept)
        self.close_btn.setVisible(False)
        button_layout.addWidget(self.close_btn)
        
        layout.addLayout(button_layout)
    
    def toggle_log(self):
        """Toggle log visibility."""
        if self.log_text.isVisible():
            self.log_text.setVisible(False)
            self.show_log_btn.setText("顯示詳細資訊")
            self.setFixedSize(400, 200)
        else:
            self.log_text.setVisible(True)
            self.show_log_btn.setText("隱藏詳細資訊")
            self.setFixedSize(400, 300)
    
    def start_download(self, download_worker: DownloadWorker):
        """Start the download with the given worker."""
        self.download_worker = download_worker
        
        # Connect signals
        download_worker.progress.connect(self.update_progress)
        download_worker.status.connect(self.update_status)
        download_worker.finished.connect(self.download_finished)
        
        # Start download
        download_worker.start()
        
        self.status_label.setText("下載中...")
        self.log_text.append("開始下載伺服器檔案...")
    
    def update_progress(self, value):
        """Update progress bar."""
        self.progress_bar.setValue(value)
    
    def update_status(self, message):
        """Update status label and log."""
        self.status_label.setText(message)
        self.log_text.append(message)
    
    def download_finished(self, success, message):
        """Handle download completion."""
        if success:
            self.status_label.setText("下載完成！")
            self.progress_bar.setValue(100)
            self.log_text.append(f"✅ {message}")
        else:
            self.status_label.setText("下載失敗")
            self.log_text.append(f"❌ {message}")
        
        # Show close button, hide cancel button
        self.cancel_btn.setVisible(False)
        self.close_btn.setVisible(True)
        
        # Clean up worker
        if self.download_worker:
            self.download_worker.deleteLater()
            self.download_worker = None
    
    def cancel_download(self):
        """Cancel the current download."""
        if self.download_worker:
            self.download_worker.cancel()
            self.download_worker.wait()  # Wait for thread to finish
            self.download_worker.deleteLater()
            self.download_worker = None
        
        self.reject()
    
    def closeEvent(self, event):
        """Handle dialog close event."""
        if self.download_worker and self.download_worker.isRunning():
            self.cancel_download()
        event.accept()
