# Minecraft Server Manager 使用指南

## 🚀 快速開始

### 1. 啟動應用程式

```bash
# 使用 uv 啟動
uv run minecraft-server-manager

# 或者直接運行 Python 模組
uv run python -m minecraft_server_manager.main

# 測試 GUI（包含歡迎訊息）
uv run python test_gui.py
```

### 2. 創建第一個伺服器

1. **點擊「創建新伺服器」按鈕**
2. **填寫基本資訊**：
   - 伺服器名稱：例如 "我的生存伺服器"
   - 資料夾：預設會在 `servers/` 下自動創建，或手動選擇
   - ✅ **自動資料夾管理**：勾選後會根據伺服器名稱自動創建資料夾
3. **設定 Java 環境**：
   - Java 路徑：可留空使用系統預設
   - 記憶體：建議至少 2GB (2048MB)
   - JVM 參數：可選，例如 `-XX:+UseG1GC`
4. **選擇伺服器類型和版本**：
   - **Vanilla**：官方原版伺服器
   - **Paper**：高效能，支援插件
   - **Fabric**：輕量級 Mod 支援
   - **Forge**：支援複雜 Mod
   - **Modpack**：整合包伺服器
   - 點擊 🔄 按鈕重新整理最新版本列表
5. **下載選項**：
   - ✅ **自動下載伺服器檔案**：勾選後會自動下載對應的伺服器 JAR 檔案
   - 對於 **Modpack**：需要輸入 CurseForge、Modrinth 等平台的下載連結
6. **點擊「創建」開始下載和設定**

### 3. 管理伺服器

#### 啟動伺服器
1. 在左側列表選擇伺服器
2. 點擊「啟動伺服器」
3. 在控制台標籤查看啟動日誌

#### 發送指令
1. 確保伺服器正在運行
2. 在控制台下方輸入框輸入指令
3. 按 Enter 或點擊「發送」

#### 常用指令範例
```
/list                    # 查看線上玩家
/op <玩家名>             # 給予玩家管理員權限
/gamemode creative <玩家> # 切換創造模式
/time set day           # 設定為白天
/weather clear          # 清除天氣
/save-all               # 保存世界
```

### 4. 編輯設定

#### 使用圖形化編輯器
1. 選擇伺服器後切換到「設定」標籤
2. 在「server.properties」子標籤中修改：
   - 伺服器描述 (MOTD)
   - 最大玩家數
   - 遊戲模式
   - 難度設定
   - PvP 開關
3. 點擊「儲存設定」

#### 直接編輯檔案
1. 切換到「檔案編輯器」子標籤
2. 選擇要編輯的檔案：
   - `server.properties` - 主要設定
   - `whitelist.json` - 白名單
   - `ops.json` - 管理員列表
   - `banned-players.json` - 封禁玩家
3. 直接編輯內容並儲存

## 📋 功能詳解

### 伺服器類型說明

| 類型 | 特點 | 適用場景 | 自動下載 |
|------|------|----------|----------|
| **Vanilla** | 官方原版，最穩定 | 純淨生存、小型伺服器 | ✅ 支援 |
| **Paper** | 高效能，豐富插件 | 大型伺服器、需要插件 | ✅ 支援 |
| **Fabric** | 輕量級，快速更新 | 現代化 Mod 支援 | ✅ 支援 |
| **Forge** | 支援複雜 Mod | 模組化遊戲體驗 | ⚠️ 需手動下載 |
| **Modpack** | 整合包伺服器 | 預配置的遊戲體驗 | 🔗 需提供連結 |

### 🆕 新功能特色

#### 🗂️ 智慧資料夾管理
- **自動創建**：根據伺服器名稱在 `servers/` 目錄下自動創建資料夾
- **同層級管理**：所有伺服器都在同一層級，便於管理和備份
- **安全命名**：自動處理特殊字元，確保資料夾名稱有效

#### 📥 自動下載系統
- **即時版本列表**：從官方 API 獲取最新版本資訊
- **一鍵下載**：自動下載對應的伺服器 JAR 檔案
- **進度顯示**：實時顯示下載進度和狀態
- **錯誤處理**：下載失敗時提供詳細錯誤資訊

#### 📦 Modpack 支援
- **多平台支援**：支援 CurseForge、Modrinth、FTB、Technic 等平台
- **連結下載**：直接輸入 Modpack 下載連結
- **自動解壓**：（規劃中）自動解壓和配置 Modpack

### 記憶體分配建議

| 玩家數量 | 建議記憶體 | 說明 |
|----------|------------|------|
| 1-5 人 | 2-4 GB | 小型私人伺服器 |
| 6-20 人 | 4-8 GB | 中型社群伺服器 |
| 21-50 人 | 8-16 GB | 大型公開伺服器 |
| 50+ 人 | 16+ GB | 超大型伺服器 |

### 常見設定說明

#### server.properties 重要設定
- **online-mode**: 正版驗證（建議開啟）
- **difficulty**: 難度（peaceful/easy/normal/hard）
- **gamemode**: 預設遊戲模式
- **max-players**: 最大玩家數
- **view-distance**: 視距（影響效能）
- **pvp**: 是否允許 PvP

## 🔧 進階功能

### 自動化管理
- **自動 EULA 接受**：創建時可選
- **自動重啟**：使用重啟按鈕
- **即時日誌監控**：控制台即時顯示

### 多伺服器管理
- 同時管理多個不同版本的伺服器
- 每個伺服器獨立的設定和資料夾
- 快速切換和比較設定

### 檔案管理
- 支援編輯所有重要配置檔案
- 語法高亮和錯誤檢查
- 自動備份（建議手動備份重要資料）

## ⚠️ 注意事項

### 首次使用

#### 🚀 快速開始（推薦）
1. **自動下載模式**：
   - ✅ 勾選「自動下載伺服器檔案」
   - 選擇伺服器類型和版本
   - 應用程式會自動下載對應的 JAR 檔案
   - 支援 Vanilla、Paper、Fabric 的自動下載

2. **手動下載模式**：
   - ❌ 取消勾選「自動下載伺服器檔案」
   - 需要手動下載對應版本的 server.jar
   - 放置在選擇的伺服器資料夾中

#### 🔧 環境需求
1. **Java 環境**：
   - 確保安裝了 Java 8 或更高版本
   - 建議使用 Java 17 或 21 (LTS 版本)
   - 可在 [Adoptium](https://adoptium.net/) 下載

2. **網路設定**：
   - 確保伺服器埠號（預設 25565）未被封鎖
   - 如需外網連接，需要設定路由器埠轉發

#### 📁 資料夾結構
```
minecraft-server-manager/
├── servers/                    # 所有伺服器的根目錄
│   ├── 我的生存伺服器/         # 自動創建的伺服器資料夾
│   │   ├── server.jar         # 自動下載的伺服器檔案
│   │   ├── server.properties  # 伺服器設定
│   │   ├── world/             # 世界資料
│   │   └── logs/              # 日誌檔案
│   └── 創造模式伺服器/
│       └── ...
└── minecraft_server_manager/   # 應用程式檔案
```

### 效能優化
1. **JVM 參數建議**：
   ```
   -XX:+UseG1GC -XX:+ParallelRefProcEnabled -XX:MaxGCPauseMillis=200
   ```

2. **伺服器設定優化**：
   - 適當調整 view-distance
   - 關閉不需要的功能
   - 定期清理世界檔案

### 安全建議
1. **定期備份**：
   - 備份世界資料夾
   - 備份設定檔案
   - 使用版本控制

2. **權限管理**：
   - 謹慎給予 OP 權限
   - 使用白名單模式
   - 定期檢查封禁列表

## 🆘 故障排除

### 常見問題

#### 🚫 伺服器無法啟動
1. **檢查 Java 環境**：
   - 確認 Java 路徑是否正確
   - 驗證 Java 版本是否符合要求
   - 嘗試在命令列執行 `java -version`

2. **檢查伺服器檔案**：
   - 確認伺服器資料夾是否有 server.jar 檔案
   - 檢查檔案是否完整（未損壞）
   - 如果使用自動下載，重新下載伺服器檔案

3. **記憶體設定**：
   - 確認記憶體分配不超過系統可用記憶體
   - 建議至少保留 1-2GB 給系統使用

4. **查看詳細錯誤**：
   - 在控制台標籤查看完整錯誤訊息
   - 檢查 logs 資料夾中的日誌檔案

#### 📥 下載問題
1. **下載失敗**：
   - 檢查網路連接是否正常
   - 嘗試重新整理版本列表
   - 手動下載並放置到伺服器資料夾

2. **版本列表空白**：
   - 點擊 🔄 按鈕重新整理
   - 檢查防火牆是否阻擋應用程式
   - 嘗試切換網路環境

3. **Forge 下載**：
   - Forge 需要手動下載
   - 前往 [MinecraftForge 官網](https://files.minecraftforge.net/)
   - 下載對應版本的 Installer 並執行

#### 🌐 玩家無法連接
1. **基本檢查**：
   - 確認伺服器正在運行
   - 檢查埠號設定（預設 25565）
   - 驗證 IP 位址是否正確

2. **網路設定**：
   - 檢查防火牆設定
   - 設定路由器埠轉發（外網連接）
   - 確認 server.properties 中的 server-ip 設定

#### ⚡ 效能問題
1. **記憶體優化**：
   - 增加記憶體分配
   - 使用推薦的 JVM 參數
   - 監控記憶體使用情況

2. **遊戲設定優化**：
   - 調整視距設定（view-distance）
   - 降低模擬距離（simulation-distance）
   - 關閉不需要的功能

3. **插件/模組問題**：
   - 檢查插件/模組相容性
   - 逐一停用插件找出問題源
   - 更新到最新版本

### 獲取幫助
- 查看控制台日誌獲取詳細錯誤資訊
- 檢查 Minecraft 官方文檔
- 參考社群論壇和 Wiki

## 📚 相關資源

- [Minecraft 官方 Wiki](https://minecraft.wiki/)
- [Paper 官方文檔](https://docs.papermc.io/)
- [Forge 官方網站](https://minecraftforge.net/)
- [Fabric 官方網站](https://fabricmc.net/)
