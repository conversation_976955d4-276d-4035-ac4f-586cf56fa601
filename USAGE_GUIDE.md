# Minecraft Server Manager 使用指南

## 🚀 快速開始

### 1. 啟動應用程式

```bash
# 使用 uv 啟動
uv run minecraft-server-manager

# 或者直接運行 Python 模組
uv run python -m minecraft_server_manager.main

# 測試 GUI（包含歡迎訊息）
uv run python test_gui.py
```

### 2. 創建第一個伺服器

1. **點擊「創建新伺服器」按鈕**
2. **填寫基本資訊**：
   - 伺服器名稱：例如 "我的生存伺服器"
   - 資料夾：選擇一個空資料夾存放伺服器檔案
3. **設定 Java 環境**：
   - Java 路徑：可留空使用系統預設
   - 記憶體：建議至少 2GB (2048MB)
   - JVM 參數：可選，例如 `-XX:+UseG1GC`
4. **選擇伺服器類型**：
   - **Vanilla (原版)**：官方原版伺服器
   - **Paper**：高效能，支援插件
   - **Forge**：支援 Mod
   - **Fabric**：輕量級 Mod 支援
5. **點擊「創建」完成**

### 3. 管理伺服器

#### 啟動伺服器
1. 在左側列表選擇伺服器
2. 點擊「啟動伺服器」
3. 在控制台標籤查看啟動日誌

#### 發送指令
1. 確保伺服器正在運行
2. 在控制台下方輸入框輸入指令
3. 按 Enter 或點擊「發送」

#### 常用指令範例
```
/list                    # 查看線上玩家
/op <玩家名>             # 給予玩家管理員權限
/gamemode creative <玩家> # 切換創造模式
/time set day           # 設定為白天
/weather clear          # 清除天氣
/save-all               # 保存世界
```

### 4. 編輯設定

#### 使用圖形化編輯器
1. 選擇伺服器後切換到「設定」標籤
2. 在「server.properties」子標籤中修改：
   - 伺服器描述 (MOTD)
   - 最大玩家數
   - 遊戲模式
   - 難度設定
   - PvP 開關
3. 點擊「儲存設定」

#### 直接編輯檔案
1. 切換到「檔案編輯器」子標籤
2. 選擇要編輯的檔案：
   - `server.properties` - 主要設定
   - `whitelist.json` - 白名單
   - `ops.json` - 管理員列表
   - `banned-players.json` - 封禁玩家
3. 直接編輯內容並儲存

## 📋 功能詳解

### 伺服器類型說明

| 類型 | 特點 | 適用場景 |
|------|------|----------|
| **Vanilla** | 官方原版，最穩定 | 純淨生存、小型伺服器 |
| **Paper** | 高效能，豐富插件 | 大型伺服器、需要插件 |
| **Forge** | 支援複雜 Mod | 模組化遊戲體驗 |
| **Fabric** | 輕量級，快速更新 | 現代化 Mod 支援 |

### 記憶體分配建議

| 玩家數量 | 建議記憶體 | 說明 |
|----------|------------|------|
| 1-5 人 | 2-4 GB | 小型私人伺服器 |
| 6-20 人 | 4-8 GB | 中型社群伺服器 |
| 21-50 人 | 8-16 GB | 大型公開伺服器 |
| 50+ 人 | 16+ GB | 超大型伺服器 |

### 常見設定說明

#### server.properties 重要設定
- **online-mode**: 正版驗證（建議開啟）
- **difficulty**: 難度（peaceful/easy/normal/hard）
- **gamemode**: 預設遊戲模式
- **max-players**: 最大玩家數
- **view-distance**: 視距（影響效能）
- **pvp**: 是否允許 PvP

## 🔧 進階功能

### 自動化管理
- **自動 EULA 接受**：創建時可選
- **自動重啟**：使用重啟按鈕
- **即時日誌監控**：控制台即時顯示

### 多伺服器管理
- 同時管理多個不同版本的伺服器
- 每個伺服器獨立的設定和資料夾
- 快速切換和比較設定

### 檔案管理
- 支援編輯所有重要配置檔案
- 語法高亮和錯誤檢查
- 自動備份（建議手動備份重要資料）

## ⚠️ 注意事項

### 首次使用
1. **下載伺服器 JAR 檔案**：
   - 應用程式不會自動下載伺服器檔案
   - 需要手動下載對應版本的 server.jar
   - 放置在選擇的伺服器資料夾中

2. **Java 環境**：
   - 確保安裝了 Java 8 或更高版本
   - 建議使用 Java 17 或 21 (LTS 版本)

3. **防火牆設定**：
   - 確保伺服器埠號（預設 25565）未被封鎖
   - 如需外網連接，需要設定路由器埠轉發

### 效能優化
1. **JVM 參數建議**：
   ```
   -XX:+UseG1GC -XX:+ParallelRefProcEnabled -XX:MaxGCPauseMillis=200
   ```

2. **伺服器設定優化**：
   - 適當調整 view-distance
   - 關閉不需要的功能
   - 定期清理世界檔案

### 安全建議
1. **定期備份**：
   - 備份世界資料夾
   - 備份設定檔案
   - 使用版本控制

2. **權限管理**：
   - 謹慎給予 OP 權限
   - 使用白名單模式
   - 定期檢查封禁列表

## 🆘 故障排除

### 常見問題

#### 伺服器無法啟動
1. 檢查 Java 路徑是否正確
2. 確認記憶體分配不超過系統可用記憶體
3. 檢查伺服器資料夾是否有 server.jar 檔案
4. 查看控制台錯誤訊息

#### 玩家無法連接
1. 檢查伺服器是否正在運行
2. 確認埠號設定正確
3. 檢查防火牆設定
4. 驗證網路連接

#### 效能問題
1. 增加記憶體分配
2. 調整視距設定
3. 優化 JVM 參數
4. 檢查插件/模組衝突

### 獲取幫助
- 查看控制台日誌獲取詳細錯誤資訊
- 檢查 Minecraft 官方文檔
- 參考社群論壇和 Wiki

## 📚 相關資源

- [Minecraft 官方 Wiki](https://minecraft.wiki/)
- [Paper 官方文檔](https://docs.papermc.io/)
- [Forge 官方網站](https://minecraftforge.net/)
- [Fabric 官方網站](https://fabricmc.net/)
