Metadata-Version: 2.4
Name: minecraft-server-manager
Version: 0.1.0
Summary: A GUI application for managing Minecraft servers
Author-email: Your Name <<EMAIL>>
License: MIT
Requires-Python: >=3.9
Requires-Dist: psutil>=5.9.0
Requires-Dist: pyside6>=6.6.0
Requires-Dist: pyyaml>=6.0.1
Requires-Dist: requests>=2.31.0
Provides-Extra: dev
Requires-Dist: black>=23.0.0; extra == 'dev'
Requires-Dist: flake8>=6.0.0; extra == 'dev'
Requires-Dist: pytest>=7.0.0; extra == 'dev'
Description-Content-Type: text/markdown

# Minecraft Server Manager

一個使用 Python 和 PySide6 開發的 Minecraft 伺服器管理工具，提供圖形化界面來創建、管理和監控多個 Minecraft 伺服器。

## 功能特色

### 🚀 伺服器管理
- **創建新伺服器**：支援多種伺服器類型（Vanilla、Paper、Forge、Fabric）
- **多伺服器管理**：在單一界面中管理多個伺服器
- **一鍵啟動/停止**：簡單的伺服器控制
- **自動 EULA 接受**：可選的自動接受 Minecraft EULA

### ⚙️ 配置管理
- **資料夾選擇**：自由選擇伺服器安裝位置
- **記憶體設定**：滑桿式 RAM 分配（512MB - 16GB）
- **Java 設定**：自訂 Java 路徑和 JVM 參數
- **版本選擇**：支援多個 Minecraft 版本

### 📊 即時監控
- **控制台顯示**：即時顯示伺服器日誌輸出
- **指令發送**：直接在 GUI 中發送伺服器指令
- **狀態監控**：即時顯示伺服器運行狀態

### 📝 設定編輯
- **server.properties 編輯器**：圖形化編輯伺服器設定
- **檔案編輯器**：直接編輯配置檔案
- **多檔案支援**：支援編輯 whitelist、ops、banned 等檔案

## 系統需求

- Python 3.9 或更高版本
- Windows 10/11（主要測試平台）
- Java 8 或更高版本（用於運行 Minecraft 伺服器）

## 安裝方式

### 使用 uv（推薦）

1. 安裝 uv：
```bash
pip install uv
```

2. 克隆專案：
```bash
git clone <repository-url>
cd minecraft-server-manager
```

3. 安裝依賴：
```bash
uv pip install -e .
```

4. 運行應用程式：
```bash
uv run minecraft-server-manager
```

### 使用 pip

1. 克隆專案：
```bash
git clone <repository-url>
cd minecraft-server-manager
```

2. 創建虛擬環境：
```bash
python -m venv venv
venv\Scripts\activate  # Windows
# 或
source venv/bin/activate  # Linux/macOS
```

3. 安裝依賴：
```bash
pip install -e .
```

4. 運行應用程式：
```bash
python -m minecraft_server_manager.main
```

## 使用說明

### 創建新伺服器

1. 點擊「創建新伺服器」按鈕
2. 填寫伺服器資訊：
   - **伺服器名稱**：為你的伺服器命名
   - **資料夾**：選擇伺服器檔案存放位置
   - **Java 路徑**：選擇 Java 執行檔（可留空使用系統預設）
   - **記憶體**：使用滑桿設定 RAM 分配
   - **遊戲版本**：選擇 Minecraft 版本
   - **伺服器類型**：選擇 Vanilla、Paper、Forge 或 Fabric
3. 點擊「創建」完成設定

### 管理伺服器

1. 在左側伺服器列表中選擇要管理的伺服器
2. 使用控制按鈕：
   - **啟動伺服器**：啟動選中的伺服器
   - **停止伺服器**：停止運行中的伺服器
   - **刪除伺服器**：從列表中移除伺服器

### 監控伺服器

1. 選擇運行中的伺服器
2. 在「控制台」標籤中：
   - 查看即時伺服器日誌
   - 在命令輸入框中輸入指令並按 Enter 發送
   - 使用控制按鈕管理伺服器狀態

### 編輯設定

1. 選擇伺服器後切換到「設定」標籤
2. 在「server.properties」子標籤中：
   - 使用圖形化界面編輯常用設定
   - 設定伺服器描述、最大玩家數、遊戲模式等
3. 在「檔案編輯器」子標籤中：
   - 選擇要編輯的檔案
   - 直接編輯檔案內容
4. 點擊「儲存設定」保存變更

## 技術架構

### 為什麼選擇 PySide6？

1. **穩定性極佳**：Qt 是成熟的商業級 GUI 框架
2. **功能豐富**：完美支援多頁面管理、即時數據更新、檔案操作
3. **跨平台**：支援 Windows、macOS、Linux
4. **效能優秀**：適合處理即時日誌輸出和多進程管理

### 專案結構

```
minecraft_server_manager/
├── __init__.py
├── main.py                 # 應用程式入口點
└── gui/
    ├── __init__.py
    ├── main_window.py      # 主視窗
    ├── server_creation_dialog.py    # 伺服器創建對話框
    ├── server_list_widget.py       # 伺服器列表組件
    ├── server_console_widget.py    # 控制台組件
    └── server_settings_widget.py   # 設定編輯組件
```

## 開發

### 運行測試

```bash
uv run pytest
```

### 程式碼格式化

```bash
uv run black .
```

### 程式碼檢查

```bash
uv run flake8
```

## 授權

MIT License

## 貢獻

歡迎提交 Issue 和 Pull Request！

## 注意事項

1. 首次運行伺服器時，需要手動下載對應的伺服器 JAR 檔案
2. 確保選擇的資料夾有足夠的讀寫權限
3. 建議為不同版本的伺服器使用不同的資料夾
4. 大型伺服器建議分配足夠的記憶體（至少 2GB）
