#!/usr/bin/env python3
"""Test script for the downloader functionality."""

import sys
from PySide6.QtWidgets import QApplication
from minecraft_server_manager.core.downloader import ServerDownloader


def test_downloader():
    """Test the downloader functionality."""
    app = QApplication(sys.argv)
    
    downloader = ServerDownloader()
    
    print("🧪 測試下載器功能")
    print("=" * 50)
    
    # Test version fetching
    server_types = ["Vanilla", "Paper", "Fabric", "Forge"]
    
    for server_type in server_types:
        print(f"\n📋 {server_type} 版本列表:")
        try:
            versions = downloader.get_available_versions(server_type)
            print(f"   找到 {len(versions)} 個版本")
            print(f"   最新版本: {versions[:3] if versions else '無'}")
        except Exception as e:
            print(f"   ❌ 錯誤: {e}")
    
    # Test download info
    print(f"\n🔗 下載資訊測試:")
    test_cases = [
        ("Vanilla", "1.20.4"),
        ("Paper", "1.20.4"),
        ("Fabric", "1.20.4"),
        ("Forge", "1.20.4")
    ]
    
    for server_type, version in test_cases:
        print(f"\n   {server_type} {version}:")
        try:
            download_info = downloader.get_download_info(server_type, version)
            if download_info:
                url, filename, size = download_info
                if url:
                    print(f"     ✅ URL: {url[:50]}...")
                    print(f"     📁 檔名: {filename}")
                    print(f"     📏 大小: {size // 1024 if size > 0 else '未知'} KB")
                else:
                    print(f"     ⚠️  需要手動下載: {filename}")
            else:
                print(f"     ❌ 無法獲取下載資訊")
        except Exception as e:
            print(f"     ❌ 錯誤: {e}")
    
    # Test modpack sources
    print(f"\n📦 Modpack 來源:")
    modpack_sources = downloader.get_modpack_sources()
    for name, url in modpack_sources.items():
        print(f"   • {name}: {url}")
    
    print(f"\n✅ 測試完成！")


if __name__ == "__main__":
    test_downloader()
