#!/usr/bin/env python3
"""Simple test script to verify GUI functionality."""

import sys
from PySide6.QtWidgets import QApplication, QMessageBox
from minecraft_server_manager.gui.main_window import MainWindow


def test_gui():
    """Test the GUI application."""
    app = QApplication(sys.argv)
    
    try:
        # Create main window
        window = MainWindow()
        
        # Show a test message
        QMessageBox.information(
            window, 
            "測試成功", 
            "Minecraft Server Manager GUI 已成功載入！\n\n"
            "功能包括：\n"
            "• 創建新伺服器\n"
            "• 管理多個伺服器\n"
            "• 即時控制台顯示\n"
            "• 設定檔編輯\n\n"
            "點擊 OK 繼續使用應用程式。"
        )
        
        # Show the main window
        window.show()
        
        # Start event loop
        sys.exit(app.exec())
        
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    test_gui()
